{
    "workbench.colorCustomizations": {
        // 编辑器区域 - 主背景色 RGB(249,251,239) = #F9FBEF
        "editor.background": "#F9FBEF",
        "editor.foreground": "#2D3748",
        
        // 侧边栏 - 稍深一点的变体
        "sideBar.background": "#F5F7E7",
        "sideBar.foreground": "#2D3748",
        "sideBar.border": "#E5E7C7",
        
        // 活动栏 - 再深一点
        "activityBar.background": "#F1F3DF",
        "activityBar.foreground": "#2D3748",
        "activityBar.border": "#E5E7C7",
        "activityBar.activeBorder": "#A0A890",
        "activityBar.activeBackground": "#EDEFD7",
        
        // 状态栏
        "statusBar.background": "#EDEFD7",
        "statusBar.foreground": "#2D3748",
        "statusBar.border": "#E5E7C7",
        "statusBar.noFolderBackground": "#EDEFD7",
        "statusBar.debuggingBackground": "#E9EBCF",
        
        // 标题栏
        "titleBar.activeBackground": "#F5F7E7",
        "titleBar.activeForeground": "#2D3748",
        "titleBar.inactiveBackground": "#F9FBEF",
        "titleBar.inactiveForeground": "#718096",
        "titleBar.border": "#E5E7C7",
        
        // 标签页
        "tab.activeBackground": "#F9FBEF",
        "tab.activeForeground": "#2D3748",
        "tab.inactiveBackground": "#F1F3DF",
        "tab.inactiveForeground": "#718096",
        "tab.border": "#E5E7C7",
        "tab.activeBorder": "#A0A890",
        "tab.hoverBackground": "#EDEFD7",
        
        // 面板区域 (终端、输出等)
        "panel.background": "#F5F7E7",
        "panel.border": "#E5E7C7",
        "panelTitle.activeBorder": "#A0A890",
        "panelTitle.activeForeground": "#2D3748",
        "panelTitle.inactiveForeground": "#718096",
        
        // 资源管理器
        "list.activeSelectionBackground": "#D2E3FE",
        "list.activeSelectionForeground": "#2D3748",
        "list.inactiveSelectionBackground": "#D2E3FE",
        "list.hoverBackground": "#F1F3DF",
        "list.focusBackground": "#D2E3FE",

        // 文件夹折叠/展开图标和颜色
        "tree.indentGuidesStroke": "#E5E7C7",
        "list.deemphasizedForeground": "#718096",
        "tree.tableColumnsBorder": "#E5E7C7",

        // 文件夹和文件图标颜色
        "symbolIcon.folderForeground": "#A0A890",
        "symbolIcon.fileForeground": "#718096",

        // 资源管理器标题和操作按钮
        "sideBarSectionHeader.background": "#F1F3DF",
        "sideBarSectionHeader.foreground": "#2D3748",
        "sideBarSectionHeader.border": "#E5E7C7",
        
        // 输入框和下拉菜单
        "input.background": "#F9FBEF",
        "input.border": "#E5E7C7",
        "input.foreground": "#2D3748",
        "dropdown.background": "#F9FBEF",
        "dropdown.border": "#E5E7C7",
        
        // 按钮
        "button.background": "#A0A890",
        "button.foreground": "#FFFFFF",
        "button.hoverBackground": "#8B9480",
        
        // 滚动条
        "scrollbar.shadow": "#E5E7C7",
        "scrollbarSlider.background": "#D1D3C7",
        "scrollbarSlider.hoverBackground": "#C5C7BB",
        "scrollbarSlider.activeBackground": "#B9BBAF",
        
        // 分割线
        "editorGroup.border": "#E5E7C7",
        "editorGroupHeader.tabsBackground": "#F1F3DF",
        "editorGroupHeader.tabsBorder": "#E5E7C7",
        
        // 通知
        "notifications.background": "#F9FBEF",
        "notifications.border": "#E5E7C7",
        "notifications.foreground": "#2D3748",
        
        // 搜索框
        "searchEditor.findMatchBackground": "#E9EBCF",
        "searchEditor.findMatchBorder": "#A0A890",
        
        // 菜单
        "menu.background": "#F9FBEF",
        "menu.foreground": "#2D3748",
        "menu.selectionBackground": "#D2E3FE",
        "menu.border": "#E5E7C7",
        
        // 编辑器组件
        "editorWidget.background": "#F9FBEF",
        "editorWidget.border": "#E5E7C7",
        "editorSuggestWidget.background": "#F9FBEF",
        "editorSuggestWidget.border": "#E5E7C7",
        "editorSuggestWidget.selectedBackground": "#D2E3FE",
        
        // 终端
        "terminal.background": "#F5F7E7",
        "terminal.foreground": "#2D3748",
        "terminal.border": "#E5E7C7",
        
        // 调试控制台
        "debugConsole.infoForeground": "#2D3748",
        "debugConsole.warningForeground": "#D69E2E",
        "debugConsole.errorForeground": "#E53E3E",
        
        // 编辑器行号和装订线
        "editorLineNumber.foreground": "#A0ADB8",
        "editorLineNumber.activeForeground": "#2D3748",
        "editorGutter.background": "#F9FBEF",
        
        // 选择和高亮
        "editor.selectionBackground": "#D2E3FE80",
        "editor.selectionHighlightBackground": "#EDEFD780",
        "editor.findMatchBackground": "#D2E3FE",
        "editor.findMatchHighlightBackground": "#F1F3DF80",
        
        // 光标
        "editorCursor.foreground": "#2D3748",
        
        // 括号匹配
        "editorBracketMatch.background": "#D2E3FE80",
        "editorBracketMatch.border": "#A0A890"
    },
    
    // 编辑器字体和主题设置
    "editor.fontFamily": "'Cascadia Code', 'Fira Code', 'JetBrains Mono', Consolas, 'Courier New', monospace",
    "editor.fontSize": 14,
    "editor.lineHeight": 1.6,
    "editor.fontLigatures": true,
    
    // 工作台设置
    "workbench.startupEditor": "welcomePage",
    "workbench.iconTheme": "material-icon-theme",
    
    // 文件资源管理器
    "explorer.compactFolders": false,
    
    // 终端设置
    "terminal.integrated.fontSize": 13,
    "terminal.integrated.fontFamily": "'Cascadia Code', 'Fira Code', 'JetBrains Mono', Consolas"
}
