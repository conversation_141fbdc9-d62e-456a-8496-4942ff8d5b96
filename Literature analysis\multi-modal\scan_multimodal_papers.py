#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模态推荐系统文献扫描脚本
遍历多模态目录，识别所有英文原版PDF文献，确保唯一性
"""

import os
import re
from pathlib import Path
from collections import defaultdict
import hashlib

def normalize_title(title):
    """标准化文献标题，用于去重"""
    # 移除文件扩展名
    title = re.sub(r'\.pdf$', '', title, flags=re.IGNORECASE)
    # 移除translation标记
    title = re.sub(r'-translation$', '', title, flags=re.IGNORECASE)
    # 标准化空格和特殊字符
    title = re.sub(r'[_\-\s]+', ' ', title)
    # 转换为小写并去除首尾空格
    return title.lower().strip()

def is_translation_file(filename):
    """判断是否为翻译版本文件"""
    return 'translation' in filename.lower()

def extract_title_from_path(file_path):
    """从文件路径提取标题"""
    filename = os.path.basename(file_path)
    # 移除.pdf扩展名
    title = re.sub(r'\.pdf$', '', filename, flags=re.IGNORECASE)
    return title

def scan_multimodal_directory(base_dir):
    """
    扫描多模态目录，返回去重后的英文原版PDF文献列表
    """
    base_path = Path(base_dir)
    if not base_path.exists():
        print(f"目录不存在: {base_dir}")
        return []
    
    # 存储所有找到的PDF文件
    all_pdfs = []
    
    # 递归遍历所有子目录
    for root, dirs, files in os.walk(base_path):
        for file in files:
            if file.lower().endswith('.pdf'):
                file_path = os.path.join(root, file)
                all_pdfs.append(file_path)
    
    print(f"总共找到 {len(all_pdfs)} 个PDF文件")
    
    # 按标题分组，用于去重
    title_groups = defaultdict(list)
    
    for pdf_path in all_pdfs:
        title = extract_title_from_path(pdf_path)
        normalized_title = normalize_title(title)
        title_groups[normalized_title].append({
            'path': pdf_path,
            'filename': os.path.basename(pdf_path),
            'title': title,
            'is_translation': is_translation_file(pdf_path)
        })
    
    # 选择每组中的最佳版本（优先英文原版，完全排除翻译版本）
    selected_papers = []

    for normalized_title, papers in title_groups.items():
        # 完全过滤掉翻译版本
        non_translation = [p for p in papers if not p['is_translation']]

        if non_translation:
            if len(non_translation) == 1:
                # 只有一个非翻译版本，直接选择
                selected_papers.append(non_translation[0])
            else:
                # 如果有多个非翻译版本，选择路径最短的（通常是主目录中的）
                selected = min(non_translation, key=lambda x: len(x['path']))
                selected_papers.append(selected)
        # 如果都是翻译版本，则跳过（不添加到selected_papers中）
    
    print(f"去重后剩余 {len(selected_papers)} 个唯一文献")
    
    # 按路径排序
    selected_papers.sort(key=lambda x: x['path'])
    
    return selected_papers

def generate_article_list(papers, output_dir):
    """生成article_list.md文档"""
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, 'article_list.md')
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# 多模态推荐系统文献清单\n\n")
        f.write(f"**总计文献数量**: {len(papers)}\n\n")
        f.write("**处理状态说明**:\n")
        f.write("- ✅ 已完成分析\n")
        f.write("- 🔄 正在处理\n")
        f.write("- ❌ 待处理\n\n")
        f.write("---\n\n")
        
        for i, paper in enumerate(papers, 1):
            f.write(f"## {i:02d}. {paper['title']}\n\n")
            f.write(f"- **文件路径**: `{paper['path']}`\n")
            f.write(f"- **文件名**: {paper['filename']}\n")
            f.write(f"- **处理状态**: ❌ 待处理\n")
            f.write(f"- **分析日期**: \n")
            f.write(f"- **备注**: \n\n")
            f.write("---\n\n")
    
    print(f"文献清单已保存到: {output_path}")
    return output_path

def main():
    # 设置路径 (WSL格式)
    base_dir = "/mnt/d/Download/article/多模态"
    output_dir = "/mnt/d/Download/article/Literature analysis/multi-modal"
    
    print("开始扫描多模态推荐系统文献...")
    print(f"扫描目录: {base_dir}")
    
    # 扫描文献
    papers = scan_multimodal_directory(base_dir)
    
    if not papers:
        print("未找到任何PDF文献")
        return
    
    # 生成文献清单
    article_list_path = generate_article_list(papers, output_dir)
    
    print("\n=== 扫描结果 ===")
    print(f"找到唯一文献: {len(papers)} 篇")
    print(f"文献清单保存位置: {article_list_path}")
    
    # 显示前10篇文献作为预览
    print("\n=== 文献预览（前10篇）===")
    for i, paper in enumerate(papers[:10], 1):
        print(f"{i:02d}. {paper['title']}")
        print(f"    路径: {paper['path']}")
        print()

if __name__ == "__main__":
    main()
